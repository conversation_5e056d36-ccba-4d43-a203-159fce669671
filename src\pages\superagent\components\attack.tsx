import React, { useRef, useEffect, useState } from "react";
import {
  Circle,
  CubicHorizontal,
  ExtensionCategory,
  Graph,
  NodeEvent,
  register,
  subStyleProps,
} from "@antv/g6";
import { Renderer } from "@antv/g-svg";
import { Button, Space, Typography } from "antd";

const { Text } = Typography;
const style = document.createElement("style");
style.innerHTML = `@import url('/fonts/iconfont.css');`;
document.head.appendChild(style);
// 节点类型定义
type NodeType = "c2" | "domain" | "file" | "process" | "server" | "exploit";

class FlyMarkerCubic extends CubicHorizontal {
  getMarkerStyle(attributes: any) {
    // 统一使用markerSize为14，简化计算
    return {
      r: (attributes.markerSize || 14) * 6,
      fill: attributes.markerColor || "#c3d5f9",
      offsetPath: this.shapeMap.key,
      ...subStyleProps(attributes, "marker"),
    };
  }

  onCreate() {
    const marker = this.upsert(
      "marker",
      Circle,
      this.getMarkerStyle(this.attributes),
      this
    );
    marker?.animate([{ offsetDistance: 0 }, { offsetDistance: 1 }], {
      duration: 3000,
      iterations: Infinity,
    });
  }
}

register(ExtensionCategory.EDGE, "fly-marker-cubic", FlyMarkerCubic);
// 节点样式配置函数
const getNodeStyle = (type: NodeType) => {
  const styles = {
    c2: {
      fill: "#F44336",
      stroke: "#F44336",
      size: 40,
      labelText: "Hover me!",
      labelFill: "#ffffffff",
      iconFontFamily: "iconfont",
      iconText: String.fromCharCode(0xe622),
    },
    domain: { fill: "#4CAF50", stroke: "#4CAF50", size: 30 },
    file: { fill: "#2196F3", stroke: "#2196F3", size: 40 },
    process: { fill: "#FFEB3B", stroke: "#FFEB3B", size: 30 },
    server: { fill: "#9C27B0", stroke: "#9C27B0", size: 35 },
    exploit: { fill: "#795548", stroke: "#795548", size: 40 },
  };
  return styles[type] || styles.domain;
};
// 原始静态数据（已注释，现在使用动态数据）
/*
const data = {
  nodes: [
    {
      id: "c2",
      data: { label: "C2\n118.195.190.7" },
      style: getNodeStyle("c2"),
      type: "diamond",
    },
    {
      id: "baidu",
      data: { label: "baidu.com" },
      style: getNodeStyle("domain"),
    },
    {
      id: "doc",
      data: { label: "个人资料.docx" },
      style: getNodeStyle("file"),
    },
    {
      id: "360safe",
      data: { label: "360SafeSvc" },
      style: getNodeStyle("process"),
    },
    {
      id: "frpc",
      data: { label: "frpc.exe" },
      style: getNodeStyle("process"),
    },
    {
      id: "data7z",
      data: { label: "data.7z" },
      style: getNodeStyle("file"),
    },
    {
      id: "beijing",
      data: { label: "北京内网\n10.106.108.110" },
      style: getNodeStyle("server"),
    },
    {
      id: "authKeys",
      data: { label: "authorized_keys" },
      style: getNodeStyle("file"),
    },
    {
      id: "wuhan",
      data: { label: "武汉外网\n172.16.21.70" },
      style: getNodeStyle("server"),
    },
    {
      id: "wuhanClient",
      data: { label: "172.16.21.71" },
      style: getNodeStyle("server"),
    },
    {
      id: "pwnkit",
      data: { label: "PwnKit-Exploit" },
      style: getNodeStyle("exploit"),
    },
  ],
  edges: [
    {
      source: "c2",
      target: "doc",
      data: { label: "下载恶意文件" },
      style: { markerSize: 14, markerColor: "#ff4757" },
    },
    {
      source: "c2",
      target: "baidu",
      data: { label: "C2 通信" },
      style: { markerSize: 4, markerColor: "#2ed573" }, // 普通连接
    },
    {
      source: "doc",
      target: "360safe",
      data: { label: "权限维持" },
      style: { markerSize: 4, markerColor: "#2ed573" },
    },
    {
      source: "360safe",
      target: "frpc",
      data: { label: "植入穿透工具" },
      style: { markerSize: 4, markerColor: "#2ed573" },
    },
    {
      source: "frpc",
      target: "beijing",
      data: { label: "内网横向移动" },
      style: { markerSize: 4, markerColor: "#2ed573" },
    },
    {
      source: "beijing",
      target: "authKeys",
      data: { label: "篡改密钥" },
      style: { markerSize: 4, markerColor: "#2ed573" },
    },
    {
      source: "beijing",
      target: "wuhan",
      data: { label: "跨区域渗透" },
      style: { markerSize: 4, markerColor: "#ffa502" }, // 关键攻击路径，最大标记
    },
    {
      source: "wuhan",
      target: "wuhanClient",
      data: { label: "横向移动" },
      style: { markerSize: 4, markerColor: "#ffa502" },
    },
    {
      source: "wuhanClient",
      target: "pwnkit",
      data: { label: "漏洞利用" },
      style: { markerSize: 4, markerColor: "#ffa502" },
    },
  ],
};
*/

// 图谱数据接口
interface GraphData {
  nodes: Array<{
    id: string;
    data: { label: string };
    style: any;
    type?: string;
  }>;
  edges: Array<{
    source: string;
    target: string;
    data: { label: string };
    style: any;
  }>;
}

// 组件Props接口
interface AttackGraphProps {
  graphData: GraphData;
}

// 攻击拓扑图组件
const AttackGraph: React.FC<AttackGraphProps> = ({ graphData }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [nodeCount, setNodeCount] = useState(0); // 跟踪节点数量
  const nodeCounterRef = useRef(100); // 使用ref来管理计数器，避免闭包问题
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const graphRef = useRef<Graph | null>(null);
  console.log(graphData, "graphData");
  // 随机数据生成函数
  const generateRandomNode = (currentCounter: number) => {
    const nodeTypes: NodeType[] = [
      "c2",
      "domain",
      "file",
      "process",
      "server",
      "exploit",
    ];
    const randomType = nodeTypes[Math.floor(Math.random() * nodeTypes.length)];
    const nodeId = `dynamic-node-${currentCounter}`;

    // 生成随机位置（在画布范围内）
    const x = Math.random() * 800 + 100; // 100-900范围
    const y = Math.random() * 400 + 100; // 100-500范围

    const labels = {
      c2: [`C2-${currentCounter}`, `动态C2-${currentCounter}`],
      domain: [`domain-${currentCounter}.com`, `动态域名-${currentCounter}`],
      file: [`file-${currentCounter}.exe`, `动态文件-${currentCounter}`],
      process: [`process-${currentCounter}`, `动态进程-${currentCounter}`],
      server: [`server-${currentCounter}`, `动态服务器-${currentCounter}`],
      exploit: [`exploit-${currentCounter}`, `动态漏洞-${currentCounter}`],
    };

    return {
      id: nodeId,
      data: {
        label:
          labels[randomType][
            Math.floor(Math.random() * labels[randomType].length)
          ],
      },
      style: {
        ...getNodeStyle(randomType),
        x,
        y,
      },
      type: randomType === "c2" ? "diamond" : undefined,
    };
  };

  const generateRandomEdge = (newNodeId: string) => {
    // 获取当前所有节点ID
    const currentNodes = graphRef.current?.getNodeData() || [];
    if (currentNodes.length === 0) return null;

    // 随机选择一个已存在的节点作为连接目标
    const randomExistingNode =
      currentNodes[Math.floor(Math.random() * currentNodes.length)];

    const edgeLabels = [
      "动态连接",
      "数据传输",
      "命令执行",
      "文件传输",
      "网络通信",
      "权限提升",
      "横向移动",
      "数据窃取",
    ];

    return {
      source: Math.random() > 0.5 ? newNodeId : randomExistingNode.id,
      target: Math.random() > 0.5 ? randomExistingNode.id : newNodeId,
      data: {
        label: edgeLabels[Math.floor(Math.random() * edgeLabels.length)],
      },
      style: {
        markerSize: 14, // 统一使用14
        markerColor: Math.random() > 0.5 ? "#ff4757" : "#2ed573",
      },
    };
  };

  const addRandomData = () => {
    if (!graphRef.current) return;

    // 使用当前的计数器值
    const currentCounter = nodeCounterRef.current;
    const newNode = generateRandomNode(currentCounter);
    const newEdge = generateRandomEdge(newNode.id);

    // 添加新节点
    graphRef.current.addNodeData([newNode]);

    // 添加新边（如果生成成功）
    if (newEdge) {
      graphRef.current.addEdgeData([newEdge]);
    }

    // 重新绘制但不重新布局
    graphRef.current.draw();

    // 更新计数器和节点数量
    nodeCounterRef.current += 1;
    setNodeCount((prev) => prev + 1);
  };

  // const startGenerating = () => {
  //   if (timerRef.current) return;

  //   setIsGenerating(true);
  //   timerRef.current = setInterval(() => {
  //     addRandomData();
  //   }, 2000); // 每2秒添加一个新节点
  // };

  // const stopGenerating = () => {
  //   if (timerRef.current) {
  //     clearInterval(timerRef.current);
  //     timerRef.current = null;
  //   }
  //   setIsGenerating(false);
  // };

  useEffect(() => {
    if (!containerRef.current) return;

    // 初始化 G6 图实例
    const graph = new Graph({
      container: containerRef.current,
      renderer: () => new Renderer(),
      background: "#212528",
      width: containerRef.current?.clientWidth || 800,
      height: containerRef.current?.clientHeight,

      layout: {
        type: "force", // 力导向布局
        preventOverlap: true, // 防止节点重叠
        nodeStrength: 5000, // 节点之间的斥力
        edgeStrength: 0, // 边的弹性系数
        iterations: 20, // 迭代次数
        animation: false, // 禁用布局动画，避免新节点触发重新布局
        linkDistance: 1000,
      },
      behaviors: [
        "drag-canvas",
        "zoom-canvas",
        {
          type: "hover-activate",
          degree: 1, // 👈🏻 Activate relations.
        },
      ],
      plugins: [
        {
          type: "tooltip",
          getContent: (e, items) => {
            let result = `<h4>Custom Content</h4>`;
            items.forEach((item) => {
              result += `<p>Type: ${JSON.stringify(item.data.label)}</p>`;
            });
            return result;
          },
        },
      ],
      edge: {
        type: "fly-marker-cubic",
        style: {
          // 静态配置
          stroke: function (datum) {
            return datum.style.markerColor;
          },
          labelFill: "#ffffffff",
          labelBackground: true,
          labelBackgroundFill: "#61578c",

          // 飞行标记配置

          // markerColor: '#ff6b6b', // 控制飞行标记的颜色

          labelBackgroundRadius: 6,
          lineWidth: 2,
          lineDash: [6, 4], // 虚线样式
          // lineDashOffset: 0,

          // 动态配置 - 箭头函数形式
          // lineWidth: (datum) => (datum.data.isImportant ? 3 : 1),
          // markerSize统一为14，无需动态配置

          // 动态配置 - 普通函数形式（可访问 graph 实例）
          //   lineDash: function (datum) {
          //     console.log(datum); // graph 实例
          //     return datum.data.type === "dashed" ? [5, 5] : [];
          //   },

          // 嵌套属性也支持动态配置
          labelText: (datum) => `边: ${datum.id}`,
          endArrow: true,
        },
      },
    });

    // 保存graph引用
    graphRef.current = graph;

    // 渲染图
    if (graph) {
      graph.setData(graphData);
      graph.render();

      // 交互事件
      graph.on(NodeEvent.POINTER_OVER, (evt: any) => {
        if (evt.target && evt.target.id && graph) {
          const nodeData = graph.getNodeData(evt.target.id);
          console.log("鼠标悬停在节点上:", nodeData.data?.label);
        }
      });
    }

    // 窗口大小变化时重绘
    const resizeHandler = () => {
      if (graphRef.current) {
        graphRef.current.setSize(
          containerRef.current?.clientWidth || 800,
          containerRef.current?.clientHeight as number
        );
      }
    };

    window.addEventListener("resize", resizeHandler);

    // 清理函数
    return () => {
      window.removeEventListener("resize", resizeHandler);
      if (graphRef.current) {
        graphRef.current.destroy();
        graphRef.current = null;
      }
      // 清理定时器
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, []);

  // 监听graphData变化，更新图谱
  useEffect(() => {
    if (graphRef.current && graphData) {
      graphRef.current.setData(graphData);
      graphRef.current.render();
      setNodeCount(graphData.nodes.length);
    }
  }, [graphData]);

  return (
    <div className="w-full h-full relative">
      {/* 控制面板 */}
      {/* <div className="absolute top-4 right-4 z-10 bg-gray-800 p-3 rounded-lg shadow-lg">
        <Space direction="vertical" size="small">
          <Text className="text-white text-sm">动态数据控制</Text>
          <Space>
            <Button
              type="primary"
              size="small"
              onClick={startGenerating}
              disabled={isGenerating}
            >
              开始生成
            </Button>
            <Button
              size="small"
              onClick={stopGenerating}
              disabled={!isGenerating}
            >
              停止生成
            </Button>
          </Space>
          <Text className="text-gray-300 text-xs">节点数量: {nodeCount}</Text>
          <Text className="text-gray-300 text-xs">
            状态: {isGenerating ? "生成中..." : "已停止"}
          </Text>
        </Space>
      </div> */}

      {/* 图表容器 */}
      <div ref={containerRef} className="w-full h-full" />
    </div>
  );
};

export default AttackGraph;
